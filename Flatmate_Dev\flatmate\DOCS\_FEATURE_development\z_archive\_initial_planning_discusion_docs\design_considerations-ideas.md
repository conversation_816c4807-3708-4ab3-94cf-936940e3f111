# Design Considerations: Update Data Left Panel

**Version**: 1.0
**Status**: Draft

This document captures design feedback and ideas for the redesign of the 'Update Data' left panel, focusing on the 'Source Files' section.

## 1. Core Principles & Constraints

- **Simplicity**: The primary UI must remain simple, uncluttered, and focused on the optimal default workflow.
- **Space Efficiency**: The layout must be compact and tablet-friendly, using vertical space efficiently.
- **Discoverability**: Advanced or secondary features should be accessible without cluttering the main interface.
- **Consistency**: The UI should maintain a consistent visual language and behavior across all modules.
- **Progressive Disclosure**: Complex or secondary features should only appear when relevant, without overwhelming the user.
- **Clarity over Implicitness**: The UI should be explicit and avoid hidden or implicit actions.
- **user-friendly** I want anyone to be able to use this, my mum should have no issues, nor my tech phobic firends who hate accounting...
## 2. UI & Layout Ideas

### 2.1. Source Selection

- **Revert to Dropdown**: Instead of two large buttons, revert to using a single `OptionMenuWithLabel` (combo box) to save space.
- **Dropdown Options**: The primary options should be:
    - `Select Files...`
    - `Select Folder...`
- **Dynamic Display**: After a selection is made, the dropdown could display a shortened version of the selected path (e.g., `.../MyProject/data.csv`).

~~### 2.2. Auto-Import Workflow~~

~~**Conditional Checkbox**: Instead of a dedicated button, an `Enable Auto-Import` checkbox appears *only* after a folder has been selected. This links the action directly to the relevant context. 
>>this could actually just appear in center dislay that way it finctions as both configuration and infomation~~

### 2.3. Help & Information

- **Tooltips**: Use tooltips on hover for all major controls to provide brief explanations.
- **Advanced Options**: A small gear icon (`⚙️`) could be placed at the top of the panel. Clicking this would navigate to a separate view or panel with more granular settings, keeping the main UI clean.
>> in prd this will be a later phase

## 3. Information Display

- **Central Panel for Details**: The left panel should only contain controls and basic info from those controls:
   Detailed context, such as the contents of a selected source folder, should be displayed in the center panel.
- **Shortened Paths**: To save space, displayed file paths should be truncated (e.g., show only the filename and its immediate parent directory).

## 4. Future Considerations & Other Options

- **Resizable Panes**: A note to ensure the main application panes (left, center, right) are user-resizable.
- **Create Master CSV**: An option, possibly in the 'Advanced' settings panel, to `Create master CSV if none exists`.

## 5. Refined Logical Flow (Dropdown-Driven)

The UI state is determined entirely by the user's selection in the main dropdown menus. This creates a fluid, non-blocking workflow.




## 7. Architectural & Cross-Cutting Concerns

### 7.1. Target Devices

-   **Primary Targets**: The application should be designed and tested for both desktop and tablet interfaces.
-   **Layout Considerations**: This implies a need for responsive or adaptive layouts that maintain usability on smaller screens and with touch-based interaction.

### 7.2. Component Architecture

-   **`SecondaryButton` Naming**: The generic name `SecondaryButton` is ambiguous. It should be renamed to something more descriptive of its function, such as `SelectOptionButton` or `ConfigurationButton`.
-   **Shared Widget Organization**: Common widgets used across different side panels (like the proposed `SelectOptionButton`) should be organized into a dedicated folder within `fm/gui/_shared_components/` (e.g., `_shared_components/panel_widgets/`) to improve reusability and maintainability.

## 8. GUI-Wide Principles & Future Revisions

### 8.1. Dual Function: Configuration and Information

-   A key principle for the GUI is that elements should serve a dual function where possible, acting as both a configuration control and an information display. For example, the `Enable Auto-Import` checkbox could appear in the center display, serving as a setting while also confirming the current status.

### 8.2. Minimized Panel Toolbar (Future Revision)

-   When the center panel is maximized, the minimized side panel/toolbar should simplify its display to show only two icons:
    1.  The icon for the currently active module.
    2.  A gear icon (`⚙️`) for settings.
