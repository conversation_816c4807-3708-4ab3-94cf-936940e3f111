# Update Data UI Redesign - Initial Planning Summary

**Date**: 2025-07-24  
**Status**: Planning Phase  
**UX Expert**: Sally  

## 🎯 **Project Overview**

This document consolidates the initial planning discussions for redesigning the Update Data module UI/UX. The goal is to create a user-friendly, intuitive interface that balances automation with user control while maintaining the application's design consistency.

## 📋 **Key Design Decisions Made**

### 1. **User-Initiated Import Workflow** ✅
- **Decision**: Implement guided, user-initiated import instead of fully automatic background processing >> correct
- **Rationale**: Provides user control, reduces confusion, and maintains predictable behavior
- **Impact**: Files are detected but require explicit user action to import

### 2. **Configuration Separation** ✅
- **Decision**: Move file path configurations (source, archive) to dedicated Settings panel
- **Rationale**: Separates one-time configuration from daily workflow actions
>>They will need little configuration
  an option can also be given in the option menu


- **Impact**: Cleaner main UI focused on primary tasks

### 3. **Dual-Function UI Elements** ✅
- **Decision**: UI elements serve as both configuration and information display where possible
- **Example**: Auto-import status appears in center display as both setting and confirmation
- **Impact**: More efficient use of screen space and clearer user feedback

## 🎨 **Core UX Principles Established**

1. **Simplicity**: Primary UI remains uncluttered, focused on optimal default workflow
2. **Space Efficiency**: Compact, tablet-friendly layout using vertical space efficiently
3. **User-Friendly**: Accessible to non-technical users ("my mum should have no issues")
4. **Progressive Disclosure**: Advanced features accessible without cluttering main interface
5. **Clarity over Implicitness**: Explicit actions, no hidden or implicit behaviors
>> there is abalnce to be had here, and the concern of simplicity is paramount 
6. **Consistency**: Maintains visual language across all modules



## 🔧 **MVP UI Requirements (Based on Original State Schema)**

### Left Panel Layout (Following User's Original Design)
- **Source Files Group**:
  - Label: "Source Files"
  - OptionMenu: "Select Folder" / "Select Files" options
  - [SELECT] button for folder/file selection
- **Archive Group** (conditional):
  - Label: "Archive" (shows when source set)
  - OptionMenu: "Same as source" / "Select folder..." options
  - [SELECT] button for custom archive location
- **Processing Options**:
  - Checkbox: "Update Database" (checked by default)
  - Checkbox: "Create Master CSV" (hidden for MVP)
- **Action Button**:
  - "PROCESS FILES" button (disabled until requirements met)

### Center Panel Enhancement
- **File Display Widget**: Existing widget shows detected files (optimize existing)
- **Monitoring Status**: Shows folder monitoring state and file queue
- **Progress Feedback**: Clear import status and completion summaries
- **Configuration Display**: "Make default and monitor..." option when folder set

### Future Considerations
- **Advanced Options**: Gear icon (⚙️) for later phase - leads to granular settings
- **Resizable Panes**: Ensure main application panes are user-resizable

## 📱 **Target Devices & Responsiveness**

- **Primary Targets**: Desktop and tablet interfaces
- **Layout Requirements**: Responsive/adaptive layouts for smaller screens and touch interaction
- **Component Architecture**: Shared widgets organized in `fm/gui/_shared_components/`

## 🔄 **MVP Workflow (Simplified)**

1. **Initial Setup**: User selects source folder via left panel controls
2. **Folder Monitoring**: App monitors selected folder for new files (no auto-import)
3. **File Detection**: New files added to queue, displayed in center panel
4. **User Review**: User sees detected files in existing file display widget
5. **User Action**: User clicks "PROCESS FILES" to import
6. **Processing**: Progress indicator during import
7. **Completion**: Summary displayed, files moved to archive folder
8. **Monitoring Continues**: App returns to monitoring state for next files

**Key MVP Principles**:
- No automatic import - user always initiates
- Folder monitoring adds files to queue only
- Existing file display widget shows queue
- Simple, predictable workflow

## 🚨 **Critical Implementation Notes**

### Must-Do's
- ✅ Finalize all design documents before any code implementation
- ✅ User must approve all design decisions before development begins
- ✅ Avoid speculative implementation - design first, code second

### User Comments to Address
- `>> auto import removed for a less confusing user controlled flow`
- `>> this could actually just appear in center display that way it functions as both configuration and information`
- `>> in prd this will be a later phase` (regarding advanced options)
- `>> note in center panel info: [] new files will be added to "import queue" #?name? design question...`
>> when the folder is set and info displayed in center panel the 
for ud data gui revision v1 prd:
[] *make default and monitor...*
option will be presented in center panel  
## 🚀 **MVP Implementation Roadmap**

### **Phase 1: Documentation & Design (CURRENT)**
0.1 **Review & Approval**: Ensure all design decisions are finalized and approved and schema definition documention has been optimised (excel worbook(s) 
1. **State Schema**: Create CSV-based state table for UI engine
2. **Component Mapping**: Map to existing widget base classes
3. **PRD Creation**: Minimal viable product requirements
4. **Implementation Guide**: Step-by-step development plan

### **Phase 2: State Engine Development**
1. **State Table (CSV)**: Define all UI states, triggers, responses
2. **State Engine**: Build engine to drive UI from state table
3. **Alternative**: Code generation engine for rapid iteration
4. **Testing Framework**: Quick iteration and testing capability

### **Phase 3: MVP Implementation**
1. **Left Panel Refactor**: Follow original state schema layout
2. **Center Panel Integration**: Optimize existing file display widget
3. **State Management**: Implement state-driven UI updates
4. **Testing & Refinement**: Rapid iteration based on state table

### **Document Creation Sequence (For Review)**
1. `mvp_state_schema.csv` - Core state table
2. `mvp_prd.md` - Product requirements document
3. `implementation_guide.md` - Technical implementation steps
4. `component_mapping.md` - Widget base class assignments
5. `testing_strategy.md` - Validation and testing approach

**KISS Principle**: Keep it simple, focus on MVP, enable rapid iteration

## 🔍 **Outstanding Design Questions**

1. **Import Queue Naming**: What should we call the list of detected files awaiting import?
2. **State Management**: How should UI states be managed and transitioned?
3. **Widget Base Classes**: Which existing shared components can be leveraged?
4. **Error Handling**: How should import errors be displayed and managed?

## 📚 **Reference Documents**

- `25723_handover1.md`: Technical context and session history
- `decision-re_auto-import.md`: Auto-import workflow decisions
- `design_considerations-ideas.md`: UI layout ideas and principles
- `design_considerations.md`: Core design constraints and mockups
- `gui_architecture_overview.md`: Overall application architecture

---

**Status**: Ready for user review and approval before proceeding to detailed schema development.
