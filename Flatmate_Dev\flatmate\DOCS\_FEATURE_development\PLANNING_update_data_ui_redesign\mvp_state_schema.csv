component_id,widget_type,base_class,state_initial,text_initial,trigger,response,text_folder_selected,notes,priority
# LEFT PANEL COMPONENTS,,,,,,,,,,
select_files_group,Group,BaseWidget,visible,,,,,Section container,HIGH
source_files_label,Label,HeadingLabel,visible,Source Files,,,Source Files,Section header,HIGH
source_dropdown,OptionMenu,OptionMenuWithLabel,active,Select Folder,user_selection,update_display,folder_name,Primary control,HIGH
source_select_btn,Button,SecondaryButton,active,[SELECT],click,open_folder_dialog,[SELECT],Folder selection trigger,HIGH
,,,,,,,,,,
select_save_group,Group,BaseWidget,hidden,,,source_folder_set,show_group,,Archive section,MEDIUM
archive_label,Label,HeadingLabel,hidden,Archive,source_folder_set,show_label,Archive,Conditional header,MEDIUM
archive_dropdown,OptionMenu,OptionMenuWithLabel,hidden,Same as source,source_folder_set,show_dropdown,Same as source,Default archive location,MEDIUM
archive_select_btn,But<PERSON>,SecondaryButton,hidden,[SELECT],click,open_folder_dialog,[SELECT],Custom archive selection,LOW
,,,,,,,,,,
update_db_checkbox,Checkbox,LabeledCheckBox,checked,Update Database,user_toggle,toggle_state,Update Database,Primary processing option,HIGH
create_master_checkbox,Checkbox,LabeledCheckBox,hidden,Create Master CSV,advanced_mode,show_checkbox,Create Master CSV,Future feature - not MVP,LOW
,,,,,,,,,,
process_button,Button,ActionButton,disabled,PROCESS FILES,files_detected,enable_button,PROCESS FILES,Main action button,HIGH
,,,,,,,,,,
# CENTER PANEL COMPONENTS,,,,,,,,,,
info_panel,Panel,BasePanelComponent,welcome,Welcome message,folder_selected,show_monitoring,Monitoring: folder_name,Main display area,HIGH
file_display_widget,Widget,BaseWidget,empty,No files detected,files_detected,show_file_list,Files ready for import,Existing widget to optimize,HIGH
monitoring_status,Label,InfoLabel,hidden,Make default and monitor,folder_selected,show_status,Make default and monitor,Configuration option,MEDIUM
,,,,,,,,,,
# STATE TRANSITIONS,,,,,,,,,,
INITIAL_TO_FOLDER_SET,,,,,folder_selected,enable_archive_group + show_monitoring,,State transition,
FOLDER_SET_TO_MONITORING,,,,,monitoring_active,start_file_watcher,,State transition,
MONITORING_TO_FILES_READY,,,,,files_detected,enable_process_button + show_file_list,,State transition,
FILES_READY_TO_PROCESSING,,,,,process_clicked,disable_ui + show_progress,,State transition,
PROCESSING_TO_COMPLETE,,,,,process_success,show_summary + move_files,,State transition,
COMPLETE_TO_MONITORING,,,,,user_acknowledge,reset_ui + continue_monitoring,,State transition,
,,,,,,,,,,
# VALIDATION RULES,,,,,,,,,,
folder_exists,,,,,folder_path,validate_folder_access,,Validation check,
files_valid,,,,,file_list,validate_file_types,,Validation check,
process_ready,,,,,ui_state,check_all_requirements,,Validation check,
,,,,,,,,,,
# WIDGET BASE CLASS MAPPING,,,,,,,,,,
ActionButton,Primary action buttons,,,,,,,Available base class,
SecondaryButton,Secondary/utility buttons,,,,,,,Available base class,
LabeledCheckBox,Checkbox with integrated label,,,,,,,Available base class,
HeadingLabel,Section headers,,,,,,,Available base class,
OptionMenuWithLabel,Dropdown with label,,,,,,,Available base class,
BasePanelComponent,Panel container base,,,,,,,Available base class,
BaseWidget,Foundation widget class,,,,,,,Available base class,
,,,,,,,,,,
# MVP REQUIREMENTS,,,,,,,,,,
NO_AUTO_IMPORT,Files detected but not automatically imported,,,,,,,MVP constraint,
USER_INITIATED,All imports require explicit user action,,,,,,,MVP constraint,
FOLDER_MONITORING,App monitors folder and adds files to queue,,,,,,,MVP constraint,
EXISTING_WIDGETS,Use existing file display widget,,,,,,,MVP constraint,
KISS_PRINCIPLE,Keep implementation simple and focused,,,,,,,MVP constraint,
