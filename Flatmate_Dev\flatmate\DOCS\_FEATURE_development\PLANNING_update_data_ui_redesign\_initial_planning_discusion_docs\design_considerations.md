# Design Considerations: Update Data Left Panel

**Version**: 1.0
**Status**: Draft

This document captures design feedback and ideas for the redesign of the 'Update Data' left panel, focusing on the 'Source Files' section.

## 1. Core Principles & Constraints

- **Simplicity**: The primary UI must remain simple, uncluttered, and focused on the optimal default workflow.
- **Space Efficiency**: The layout must be compact and tablet-friendly, using vertical space efficiently.
- **Discoverability**: Advanced or secondary features should be accessible without cluttering the main interface.

## 2. UI & Layout Ideas

### 2.1. Source Selection

- **Revert to Dropdown**: Instead of two large buttons, revert to using a single `OptionMenuWithLabel` (combo box) to save space.
- **Dropdown Options**: The primary options should be:
    - `Select Files...`
    - `Select Folder...`
- **Dynamic Display**: After a selection is made, the dropdown could display a shortened version of the selected path (e.g., `.../MyProject/data.csv`).


### 2.3. Help & Information

- **Tooltips**: Use tooltips on hover for all major controls to provide brief explanations.
- **Advanced Options**: A small gear icon (`⚙️`) could be placed at the top of the panel. Clicking this would navigate to a separate view or panel with more granular settings, keeping the main UI clean.
>> in prd this will be a later phase 
## 3. Information Display

- **Central Panel for Details**: The left panel should only contain controls. Detailed context, such as the contents of a selected source folder, should be displayed in the center panel.
- **Shortened Paths**: To save space, displayed file paths should be truncated (e.g., show only the filename and its immediate parent directory).

## 4. Future Considerations & Other Options

- **Resizable Panes**: A note to ensure the main application panes (left, center, right) are user-resizable.
- **Create Master CSV**: An option, possibly in the 'Advanced' settings panel, to `Create master CSV if none exists`.

## 5. Refined Logical Flow (Dropdown-Driven)

The UI state is determined entirely by the user's selection in the main dropdown menu. This creates a fluid, non-blocking workflow.



## 6. Final Mockup

**State 1: Initial View**
```text
+-------------------------------------------+
|  Update Data                          ⚙️  |
|  ---------------------------------------  |
|                                           |
|  1. Source Files                          |
|  +-------------------------------------+  |
|  | Select Files...          |  [v]   |  |  <-- Dropdown is key control
|  +-------------------------------------+  |
|                                           |
|  ... (rest of the panel) ...              |
+-------------------------------------------+
```

**State 2: After Folder is Selected**
```text
+-------------------------------------------+
|  Update Data                          ⚙️  |
|  ---------------------------------------  |
|                                           |
|  1. Source Files                          |
|  +-------------------------------------+  |
|  | .../MyStatements/Kiwibank  |  [v]   |  |  <-- Dropdown shows path, remains enabled
|  +-------------------------------------+  |
|  ... other options
|                                           |
|  ... (rest of the panel) ...              |
+-------------------------------------------+
```
>> auto import removed for a less confusiong user controlled flow

>> note in center panel info:  [] new files will be added to "import queue" #?name? design question...

