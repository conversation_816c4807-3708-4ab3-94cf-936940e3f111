# Update Data UI Redesign - Development Roadmap

**Date**: 2025-07-24  
**Status**: Planning Phase  
**Focus**: MVP State-Driven Architecture  

## 🎯 **Development Strategy**

### **State-Driven Architecture Approach**
- **CSV State Tables**: Define UI behavior in machine-readable CSV format
- **State Engine**: Build engine to drive UI from state definitions
- **Rapid Iteration**: Enable quick changes via CSV updates without code changes
- **Alternative**: Code generation engine for even faster iteration

## 🧩 **Widget Architecture Plan**

### **Current Widget Inventory (Verified)**

#### **Available Shared Components** (`fm/gui/_shared_components/widgets/`)
- **Buttons**: `ActionButton`, `SecondaryButton`, `ExitButton`
- **Checkboxes**: `LabeledCheckBox`
- **Labels**: `HeadingLabel`, `SubheadingLabel`, `InfoLabel`
- **Option Menus**: `OptionMenuWithLabel`, `OptionMenuWithLabelAndButton`
- **Selectors**: `AccountSelector`
- **Filters**: `DateFilterPane`

#### **Current Update Data Left Panel Variables** (From `widgets.py`)
```python
# Current widget instance names in LeftPanelButtonsWidget:
self.title = HeadingLabel("Update Data")
self.source_menu = OptionMenuWithLabel(label_text="1. Source Files", ...)
self.source_select_btn = SecondaryButton("Select...")
self.save_menu = OptionMenuWithLabel(label_text="2. Save Location", ...)
self.save_select_btn = SecondaryButton("Select...")
self.process_label = SubheadingLabel("3. Process")
self.db_update_checkbox = LabeledCheckBox("Update Database")
self.process_btn = ActionButton("Process Files")
self.cancel_btn = ExitButton("Exit")
```

### **Proposed Base Widget for Select Groups**

#### **SelectGroupWidget** (New Component)
**Purpose**: Configurable widget combining label + option menu + select button
**Location**: `fm/modules/update_data/_view/left_panel/widgets/` (initially)
**Future**: Escalate to `fm/gui/_shared_components/` once tested and optimized

**Configuration Options**:
- Label text and styling
- Option menu choices and default selection
- Button text and behavior
- Enable/disable states
- Visibility conditions

**Benefits**:
- Reduces code duplication (source_menu + source_select_btn pattern)
- Consistent styling and behavior
- Easy configuration by calling code
- Reusable across different contexts

#### **Implementation Phases**

**Phase 1: Local Implementation**
1. Create `SelectGroupWidget` in update_data module
2. Build on existing shared widgets (`OptionMenuWithLabel` + `SecondaryButton`)
3. Test and refine in update_data context
4. Validate configuration approach
>> define a configure class to map to qt 
**Phase 2: Optimization & Testing**
1. Refine configuration interface
2. Add comprehensive testing
3. Document usage patterns
4. Gather feedback from actual usage

**Phase 3: Shared Component Migration**
1. Move to `fm/gui/_shared_components/widgets/`
2. Update imports across codebase
3. Add to shared component documentation
4. Create usage examples
>> may nly be used in update data possibly categorise 

## 📊 **State Schema Implementation**

### **CSV-Based State Management**
- **Component States**: Define all UI component states and transitions
- **Trigger Mapping**: Map user actions to state changes
- **Validation Rules**: Define state transition validation
- **Widget Mapping**: Map logical components to actual widget instances

### **State Engine Architecture**
```python
# Proposed structure:
class UIStateEngine:
    def __init__(self, state_csv_path):
        self.state_table = self.load_state_table(state_csv_path)
        self.current_state = "INITIAL"
        
    def process_trigger(self, trigger, context=None):
        # Look up state transitions in CSV
        # Validate transition
        # Update UI components
        # Emit state change events
```

## 🚀 **Implementation Sequence**

### **Phase 1: Foundation (Current)**
1. ✅ **State Schema CSV**: Define complete UI behavior
2. ✅ **Excel Documentation**: Human-readable state tables
3. **State Engine**: Build CSV-driven UI engine
4. **Widget Mapping**: Connect logical components to actual widgets

### **Phase 2: MVP Implementation**
1. **SelectGroupWidget**: Create configurable select group component
2. **Left Panel Refactor**: Implement state-driven left panel
3. **Center Panel Integration**: Connect to existing file display widget
4. **State Transitions**: Implement all defined state changes

### **Phase 3: Testing & Refinement**
1. **Unit Tests**: Test state engine and components
2. **Integration Tests**: Test complete UI workflows
3. **User Testing**: Validate UX with actual usage
4. **Performance Optimization**: Optimize state engine performance

### **Phase 4: Documentation & Handoff**
1. **Component Documentation**: Document SelectGroupWidget usage
2. **State Engine Guide**: Document state table format and engine usage
3. **Migration Guide**: Document transition from current implementation
4. **Future Enhancements**: Document potential improvements

## 📝 **Key Design Decisions**

### **Widget Naming Conventions**
- **Instance Names**: Follow current pattern (`source_menu`, `source_select_btn`)
- **Class Names**: PascalCase for widget classes (`SelectGroupWidget`)
- **CSV IDs**: snake_case for state table references (`source_group`, `save_group`)

### **Configuration Strategy**
- **Declarative**: Define behavior in CSV, not code
- **Flexible**: Support multiple configuration patterns
- **Testable**: Enable easy testing of different configurations
- **Maintainable**: Clear separation of behavior and implementation

### **Migration Strategy**
- **Incremental**: Replace components one at a time
- **Backward Compatible**: Maintain existing interfaces during transition
- **Validated**: Test each change thoroughly before proceeding
- **Documented**: Document all changes and decisions

## 🔄 **Iteration Approach**

### **Rapid Prototyping Cycle**
1. **Update CSV**: Modify state definitions
2. **Test Changes**: Validate behavior with state engine
3. **Refine**: Adjust based on testing results
4. **Document**: Update documentation with changes

### **Feedback Integration**
- **User Feedback**: Incorporate UX feedback into state definitions
- **Technical Feedback**: Adjust architecture based on implementation learnings
- **Performance Feedback**: Optimize based on actual usage patterns

---

**Next Steps**: Create Excel workbook with formatted state tables for human review and CSV export for machine processing.
