# Flatmate GUI: Architecture & UX Overview

This document serves as an onboarding guide for developers working on the Flatmate GUI. It outlines the core architectural concepts, application modules, and key UX principles.

## 1. Core UI Architecture: The `Main_Window` Shell

The entire application is built around a central `Main_Window` container. This window acts as a shell that hosts the various application modules. Its layout is designed for consistency and clear separation of concerns.

**Key Features:**

- **Center Panel**: The primary workspace where the main content of the current module is displayed (e.g., the welcome screen, the file import list, the transaction table).
- **Wing Panels (Left/Right)**: Collapsible side panels used for controls, filters, and secondary information. The left panel is consistently used for module-specific actions and filters.
- **Navigation Bar**: A vertical bar on the right providing global navigation between the main application modules and access to settings.

### Layout Diagram (Mermaid)

This diagram shows the relationship between the main shell components and their typical content.

```mermaid
flowchart LR
    subgraph "Application Window"
        direction LR

        LSB["Left Sidebar"] <--> LP["Left Panel"]
        LP -- hosts --> Controls["Module-specific<br/>Filters & Controls"]
        
        LP <--> CP["Center Panel (Workspace)"]
        CP -- hosts --> Module["Active Module<br/>(Home, Update, etc.)"]

        CP <--> RP["Right Panel"]
        RP <--> RSB["Right Sidebar (Nav Bar)"]
        RSB -- contains --> Nav["Global Navigation<br/>Settings Icon"]
    end
```

### Layout Diagram (ASCII Alternative)

This text-based diagram shows the same structure and is guaranteed to render correctly in all environments.

```text
+------------------------------------------------------------------------------------+
|                                Main Application Window                             |
+----------------+---------------------+---------------------+---------------------+------------------+
|                |                     |                     |                     |                  |
|  [Left Sidebar]  | [Left Panel]        | [Center Panel]      | [Right Panel]       | [Right Sidebar]  |
|                |                     |                     |                     |                  |
|                | - Module-specific   | - Main module view  | - (Available for    | - Global Nav     |
|                |   filters &         |   (e.g., table,     |   future use)       |   Icons          |
|                |   controls          |   import list)      |                     | - Settings Icon  |
|                |                     |                     |                     |                  |
+----------------+---------------------+---------------------+---------------------+------------------+
```

## 2. Application Modules

The application is divided into distinct, task-oriented modules.

### A. Home Module

- **Purpose**: The application's landing page. It serves as the primary onboarding screen for new users.
- **Content**: Displays a welcome message and provides high-level navigation to key areas like managing profiles, viewing accounts, or updating data.
- **UX Note**: This is an ideal place to guide first-time users through initial setup, such as configuring their import folder.

### B. Update Data Module

- **Purpose**: To import new financial statements into the application.
- **Content**: Displays a file browser, status of detected files, and controls for initiating the import process.
- **Workflow**: This module is central to the application's data flow. As discussed, its UX is being refined to separate file detection from user-initiated import and to move configuration to a dedicated settings area.

### C. Categorise Module

- **Purpose**: The primary interface for viewing, searching, filtering, and managing transaction data.
- **Content**: Features a powerful table view with a prominent search bar and filtering controls in the left panel.
- **Functionality**: This is where users will spend most of their time analysing their finances, categorising transactions, and preparing data for export.

## 3. User Flow & Navigation

The logical flow for a user is sequential, but the application allows for flexibility after the initial setup.

1. **First Use**: `Home` -> `Update Data` -> `Categorise`.
   - The user starts at the welcome screen, is guided to import their first statements, and then moves to view and categorise them.
2. **Subsequent Use**: The application should be intelligent.
   - **Default**: Open the last-used module.
   - **Context-Aware**: If new statement files are detected in the import folder on launch, the app should automatically navigate to the `Update Data` module to streamline the import process.

## 4. General UX Principles

- **Consistency**: The application maintains a strong, consistent visual style and layout, which is a major strength.
- **Separation of Concerns**: The key to improving the UX is to consistently apply the principle of separating *configuration* from *action*. One-time settings (like folder paths) should live in the global `Settings` panel, not within the primary workflow of a module.
- **Clarity over Implicitness**: The auto-import redesign is a good example of this. Instead of having a hidden process, the UI will now explicitly notify the user and wait for their command. This builds trust and reduces confusion.
- **Onboarding**: The Home module can be enhanced with a simple checklist for new users to ensure they complete the necessary setup steps to get the most out of the application.

## 5. Advanced UI/UX & Dynamic Behaviour

This section outlines the design considerations for a more polished and dynamic user experience.

### A. Window Chrome & Title Bar
- **Challenge**: The default white OS title bar is visually jarring and inconsistent with the application's dark theme.
- **Goal**: Implement a custom, theme-able title bar to create a seamless, professional application window.

### B. Dynamic Panel Management
- **Default State**: Side panels (Left and Right) should be minimised by default to maximise the workspace of the Center Panel, which is especially critical for table-heavy modules like `Categorise`.
- **Minimised as Toolbar**: In their minimised state, the panels should present as a thin vertical toolbar. This toolbar is the interactive element that expands the full panel.
- **Interaction Model**: Clicking the minimised toolbar will trigger an animation to smoothly expand the panel to its default width. The Center Panel will resize concurrently.

### C. Animation & Transitions
- **Core Principle**: Avoid jarring state changes. All transitions should be smooth and provide visual feedback.
- **Patterns**:
    - **Fades**: UI elements should fade in/out gracefully when appearing or disappearing.
    - **Slides**: Panels and panes should slide smoothly during resize operations or when being expanded/collapsed.
    - **Responsive Sizing**: Icons and controls should resize proportionally as the window dimensions change, maintaining a balanced layout (as is already implemented in the Nav Bar).

### D. Responsive Design
- **Iconography**: Continue to build on the responsive icon sizing foundation to ensure all graphical elements adapt to different window sizes and resolutions.
- **Layout Fluidity**: Ensure that as panels resize, the content within them (buttons, filters, etc.) reflows logically and remains usable.
